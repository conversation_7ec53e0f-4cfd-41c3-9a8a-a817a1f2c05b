package org.sounfury.aki.infrastructure.chatsession;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.sounfury.aki.domain.chatsession.*;
import org.sounfury.aki.domain.chatsession.service.SessionManager;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * 会话管理器默认实现
 * 直接实现SessionManager接口，简洁明了
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DefaultSessionManager implements SessionManager {
    
    private final SessionRepository sessionRepository;
    
    @Override
    public ChatSession createSession(SessionId sessionId, ChatMode mode, String characterId, boolean isOwnerSession) {
        log.debug("创建会话: sessionId={}, mode={}, characterId={}, isOwner={}",
                sessionId.getValue(), mode, characterId, isOwnerSession);

        ChatSession session = ChatSession.createForMode(sessionId, mode, characterId, isOwnerSession);
        sessionRepository.save(session);

        log.info("会话创建成功: {}, 配置: {}",
                sessionId.getValue(), session.getConfigurationSummary());

        return session;
    }

    @Override
    public Optional<ChatSession> getSession(SessionId sessionId) {
        return sessionRepository.findById(sessionId);
    }
    
    @Override
    public boolean updateLastActiveTime(SessionId sessionId) {
        Optional<ChatSession> sessionOpt = sessionRepository.findById(sessionId);
        if (sessionOpt.isPresent()) {
            ChatSession updatedSession = sessionOpt.get().updateLastActiveTime();
            sessionRepository.save(updatedSession);
            log.debug("更新会话活跃时间: {}", sessionId.getValue());
            return true;
        }
        
        log.debug("会话不存在，无法更新活跃时间: {}", sessionId.getValue());
        return false;
    }
    
    @Override
    public boolean sessionExists(SessionId sessionId) {
        return sessionRepository.exists(sessionId);
    }
    
    @Override
    public boolean deleteSession(SessionId sessionId) {
        if (sessionRepository.exists(sessionId)) {
            sessionRepository.deleteById(sessionId);
            log.info("删除会话: {}", sessionId.getValue());
            return true;
        }
        
        log.debug("会话不存在，无法删除: {}", sessionId.getValue());
        return false;
    }
    
    @Override
    public boolean recordConversationRound(SessionId sessionId) {
        Optional<ChatSession> sessionOpt = sessionRepository.findById(sessionId);
        if (sessionOpt.isPresent()) {
            ChatSession updatedSession = sessionOpt.get().incrementConversationRounds();
            sessionRepository.save(updatedSession);
            log.debug("记录对话轮次: {}, 当前轮次: {}", 
                    sessionId.getValue(), updatedSession.getConversationRounds());
            return true;
        }

        log.debug("会话不存在，无法记录对话轮次: {}", sessionId.getValue());
        return false;
    }

    @Override
    public boolean resetConversationRounds(SessionId sessionId) {
        Optional<ChatSession> sessionOpt = sessionRepository.findById(sessionId);
        if (sessionOpt.isPresent()) {
            // 创建新的会话实例，轮次重置为0
            ChatSession session = sessionOpt.get();
            ChatSession resetSession = ChatSession.createForMode(
                    session.getSessionId(), 
                    session.getMode(), 
                    session.getCharacterId(), 
                    session.isOwnerSession()
            );
            sessionRepository.save(resetSession);
            log.debug("重置会话对话轮次: {}", sessionId.getValue());
            return true;
        }

        log.debug("会话不存在，无法重置对话轮次: {}", sessionId.getValue());
        return false;
    }
}
