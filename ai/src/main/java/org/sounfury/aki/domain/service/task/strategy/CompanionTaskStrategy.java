package org.sounfury.aki.domain.service.task.strategy;

import lombok.extern.slf4j.Slf4j;
import org.sounfury.aki.application.task.dto.BaseTaskRequest;
import org.sounfury.aki.application.task.dto.CompanionTaskRequest;
import org.sounfury.aki.domain.advisor.service.AdvisorConfigurationService;
import org.sounfury.aki.domain.llm.service.AdvisorAwareChatClientManager;
import org.springframework.stereotype.Component;

/**
 * 陪伴任务策略
 * 处理发布祝贺、登录欢迎等任务
 */
@Slf4j
@Component
public class CompanionTaskStrategy extends AbstractTaskStrategy {
    
    public CompanionTaskStrategy(AdvisorAwareChatClientManager chatClientManager,
                                AdvisorConfigurationService configurationService) {
        super(chatClientManager, configurationService);
    }
    
    @Override
    protected boolean isValidRequest(BaseTaskRequest request) {
        return request instanceof CompanionTaskRequest;
    }
    
    @Override
    protected String getExpectedRequestType() {
        return "CompanionTaskRequest";
    }
    
    @Override
    protected String getUserInput(BaseTaskRequest request) {
        CompanionTaskRequest companionRequest = (CompanionTaskRequest) request;
        return companionRequest.getContextInfo();
    }
    
    @Override
    protected String getStrategyName() {
        return "CompanionTaskStrategy";
    }
}
