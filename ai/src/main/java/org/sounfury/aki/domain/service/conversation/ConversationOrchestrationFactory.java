package org.sounfury.aki.domain.service.conversation;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.sounfury.aki.domain.service.conversation.strategy.AgentConversationStrategy;
import org.sounfury.aki.domain.service.conversation.strategy.ChatConversationStrategy;
import org.sounfury.aki.domain.service.conversation.strategy.ConversationStrategy;
import org.sounfury.aki.domain.chatsession.ChatMode;
import org.springframework.stereotype.Component;

/**
 * 对话编排工厂
 * 根据聊天模式创建对应的对话策略
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ConversationOrchestrationFactory {
    
    private final ChatConversationStrategy chatConversationStrategy;
    private final AgentConversationStrategy agentConversationStrategy;
    
    /**
     * 根据聊天模式获取对话策略
     * @param mode 聊天模式
     * @return 对话策略
     */
    public ConversationStrategy getStrategy(ChatMode mode) {
        if (mode == null) {
            log.warn("聊天模式为空，使用默认对话策略");
            return chatConversationStrategy;
        }
        
        ConversationStrategy strategy = switch (mode) {
            case CHAT -> chatConversationStrategy;
            case AGENT -> agentConversationStrategy;
        };
        
        log.debug("获取对话策略: 模式={}, 策略={}", mode.getName(), strategy.getStrategyName());
        return strategy;
    }
    
    /**
     * 检查模式是否支持
     * @param mode 聊天模式
     * @return 是否支持
     */
    public boolean isSupported(ChatMode mode) {
        return mode != null && (mode == ChatMode.CHAT || mode == ChatMode.AGENT);
    }
}
