package org.sounfury.aki.domain.service.task.strategy;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.sounfury.aki.application.task.dto.BaseTaskRequest;
import org.sounfury.aki.domain.advisor.config.AdvisorConfiguration;
import org.sounfury.aki.domain.advisor.service.AdvisorConfigurationService;
import org.sounfury.aki.domain.chatsession.ChatMode;
import org.sounfury.aki.domain.llm.service.AdvisorAwareChatClientManager;
import org.sounfury.aki.domain.prompt.template.TemplateType;
import org.sounfury.aki.domain.prompt.template.service.SystemPromptManager;
import org.sounfury.aki.domain.prompt.template.service.CharacterPromptManager;
import org.sounfury.aki.domain.service.conversation.strategy.ConversationStrategy;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.model.ChatResponse;
import reactor.core.publisher.Flux;

import java.time.LocalDateTime;

/**
 * 抽象任务策略模板类
 * 提供通用的任务执行流程，子类只需实现特定的业务逻辑
 */
@Slf4j
@RequiredArgsConstructor
public abstract class AbstractTaskStrategy implements TaskStrategy {

    protected final AdvisorAwareChatClientManager chatClientManager;
    protected final AdvisorConfigurationService configurationService;
    
    @Override
    public final TaskResult execute(BaseTaskRequest request) {
        LocalDateTime startTime = LocalDateTime.now();
        log.info("开始执行{}任务，任务模式: {}", getStrategyName(), request.getTaskMode());
        
        try {
            // 1. 验证请求类型
            if (!isValidRequest(request)) {
                return TaskResult.failure("请求类型错误，期望" + getExpectedRequestType(), getStrategyName());
            }
            
            // 2. 获取用户输入内容（由子类实现）
            String userInput = getUserInput(request);
            if (userInput == null || userInput.trim().isEmpty()) {
                return TaskResult.failure("无法获取输入内容", getStrategyName());
            }
            
            // 3. 构建Task专用的Advisor配置
            AdvisorConfiguration config = buildTaskConfiguration(request);

            // 4. 获取配置驱动的ChatClient
            ChatClient chatClient = chatClientManager.createChatClient(config, ChatMode.CHAT);

            // 5. 调用ChatClient
            ChatResponse chatResponse = chatClient
                    .prompt()
                    .user(userInput)
                    .call()
                    .chatResponse();
            String response = chatResponse.getResult().getOutput().getText();
            
            log.info("{}任务执行完成，任务模式: {}, 耗时: {}ms", 
                    getStrategyName(), request.getTaskMode(),
                    java.time.Duration.between(startTime, LocalDateTime.now()).toMillis());
            
            return TaskResult.success(response, getStrategyName());
            
        } catch (Exception e) {
            log.error("{}任务执行失败，任务模式: {}", getStrategyName(), request.getTaskMode(), e);
            return TaskResult.failure(e.getMessage(), getStrategyName());
        }
    }
    
    @Override
    public final Flux<String> executeStream(BaseTaskRequest request) {
        log.info("开始执行流式{}任务，任务模式: {}", getStrategyName(), request.getTaskMode());
        
        try {
            // 1. 验证请求类型
            if (!isValidRequest(request)) {
                return Flux.error(new IllegalArgumentException("请求类型错误，期望" + getExpectedRequestType()));
            }
            
            // 2. 获取用户输入内容（由子类实现）
            String userInput = getUserInput(request);

            // 3. 构建Task专用的Advisor配置
            AdvisorConfiguration config = buildTaskConfiguration(request);

            // 4. 获取配置驱动的ChatClient
            ChatClient chatClient = chatClientManager.createChatClient(config, ChatMode.CHAT);

            // 5. 调用流式接口
            return chatClient
                    .prompt()
                    .user(userInput)
                    .stream()
                    .content()
                    .doOnComplete(() -> {
                        log.info("流式{}任务执行完成，任务模式: {}", getStrategyName(), request.getTaskMode());
                    })
                    .doOnError(error -> {
                        log.error("流式{}任务执行失败，任务模式: {}", getStrategyName(), request.getTaskMode(), error);
                    });
            
        } catch (Exception e) {
            log.error("流式{}任务执行异常，任务模式: {}", getStrategyName(), request.getTaskMode(), e);
            return Flux.error(e);
        }
    }
    
    /**
     * 构建Task专用的Advisor配置
     * Task策略明确禁用记忆功能，但需要角色卡
     */
    private AdvisorConfiguration buildTaskConfiguration(BaseTaskRequest request) {
        // 创建一个模拟的ConversationRequest来复用配置服务
        ConversationStrategy.ConversationRequest mockRequest = new ConversationStrategy.ConversationRequest(
                null, // session - Task不需要session
                request.getUserName(),
                "", // userInput - 这里不重要，因为我们只用来构建配置
                request.getIsOwner() != null ? request.getIsOwner() : false
        );

        // 使用配置服务构建基础配置
        AdvisorConfiguration baseConfig = configurationService.buildConfiguration(mockRequest);

        // 为Task定制配置：明确禁用记忆和RAG，但保留角色
        return AdvisorConfiguration.builder()
                .characterId("bartender") // TODO: 从配置表中读取默认角色ID
                .userName(request.getUserName())
                .enableRag(false)        // Task明确禁用RAG
                .enableMemory(false)     // Task明确禁用记忆
                .behaviorType(TemplateType.TASK_BEHAVIOR)
                .ragSettings(null)       // 不需要RAG设置
                .memorySettings(null)    // 不需要记忆设置
                .build();
    }
    

    
    // ========== 抽象方法，由子类实现 ==========
    
    /**
     * 验证请求类型是否正确
     * @param request 请求对象
     * @return 是否为有效请求
     */
    protected abstract boolean isValidRequest(BaseTaskRequest request);
    
    /**
     * 获取期望的请求类型名称（用于错误提示）
     * @return 请求类型名称
     */
    protected abstract String getExpectedRequestType();
    
    /**
     * 获取用户输入内容
     * @param request 请求对象
     * @return 用户输入内容
     */
    protected abstract String getUserInput(BaseTaskRequest request);
    
    /**
     * 获取策略名称
     * @return 策略名称
     */
    protected abstract String getStrategyName();
}
