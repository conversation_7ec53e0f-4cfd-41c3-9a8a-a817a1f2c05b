package org.sounfury.aki.domain.chatsession;

/**
 * 聊天模式枚举
 * 定义聊天会话的模式类型
 */
public enum ChatMode {
    
    /**
     * 对话模式
     * 特征：开启记忆、角色卡、用户称呼，不开启工具调用
     */
    CHAT("chat", "对话模式"),
    
    /**
     * Agent模式
     * 特征：开启记忆、角色卡、工具调用、用户称呼
     */
    AGENT("agent", "Agent模式");
    
    private final String code;
    private final String name;
    
    ChatMode(String code, String name) {
        this.code = code;
        this.name = name;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getName() {
        return name;
    }
    
    /**
     * 根据代码获取模式
     */
    public static ChatMode fromCode(String code) {
        for (ChatMode mode : values()) {
            if (mode.code.equals(code)) {
                return mode;
            }
        }
        throw new IllegalArgumentException("未知的聊天模式代码: " + code);
    }
}
