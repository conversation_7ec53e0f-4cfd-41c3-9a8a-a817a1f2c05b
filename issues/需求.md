博客陪伴式 AI 模块设计
架构概览

博客陪伴式 AI 模块将作为一个独立的 Spring Boot REST 服务部署，负责与博客系统进行交互，为博客用户提供对话式智能陪伴。该模块采用单体架构（无需 Spring Cloud），通过 Spring WebClient 远程调用博客的 REST API 获取和修改文章数据。主要功能包括：

    日常对话：与用户进行带有人设的闲聊，对用户消息作出个性化回应。

    Agent 模式：在对话中具有人设的同时，具备操作博客的能力（增删改查博文）。当用户请求执行博文管理操作时，AI 可以调用博客 API 完成操作。

    任务模式：由前端按钮触发的固定任务，例如总结当前博文、提取博文要点，或在用户发表文章后自动给出鼓励性回复等。这些任务以预定义提示词调用 AI，生成相应结果，同时为了遵循陪伴的概念，人设即使是固定任务也要有。


人设角色卡与系统提示词

角色卡定义了 AI 扮演的虚拟人格。在设计中，我们为不同的角色创建预设的角色卡，包括角色的名称和详细人设描述（性格、说话风格、背景等）。系统将使用一个全局的提示词模板将角色卡信息融合进对话上下文。例如，全局基础提示词规定 AI 的基本行为准则，模板部分则可能如下：

你是一位世界一流的演员，你将扮演的角色是「%s」，人设是：%s。

其中第一个占位符插入角色名称，第二个占位符插入角色的人设描述文本。通过这种方式，每次对话开始时，AI 的系统消息都会包含角色设定，使得回复始终符合该角色的语气和个性。

实现上，PersonaService 获取角色卡后，会构造上述系统提示词，并在每次模型调用时作为系统信息注入。对于不同角色，只需切换角色卡，即可改变 AI 的人格和行为模式。世界书也可以与角色卡绑定：当用户选择某个角色后，系统自动加载该角色关联的知识库，使AI拥有相应背景设定（如角色的世界观、设定集）作为对话依据
。这样角色的人设贯穿始终，不论是闲聊、Agent操作还是任务场景，AI 都以相同的人格出发，保证连续一致的体验。
多轮对话记忆管理

为了让 AI 在长对话中保持上下文连贯，模块设计了完善的记忆管理策略：

    短期对话记忆：利用 Spring AI 的 ChatMemoryAdvisor，将对话历史自动加入提示中
    springdoc.cn
    。对于每次用户提问，系统从 ChatMemory 中提取最近若干轮对话作为上下文。一方面，游客用户的对话记忆仅存在于当前会话的内存中（会话刷新或页面重载即清空）；另一方面，站长用户的对话记忆则持久化在数据库中，使用会话ID或用户ID索引，确保即使刷新页面或下次访问，仍可加载之前的对话继续交流。MemoryService 会控制保存的历史长度（或通过 Token 限制），必要时丢弃最早的内容或进行摘要，以适应模型上下文长度限制。

    长期重点记忆：除了逐轮的对话历史，还允许标记重点记忆，例如用户透露的个人偏好、重要经历等。这些重点记忆量不大但对个性化回复很关键，系统会将其长期保存（尤其对站长用户）。在每次生成回复时，MemoryService 都会将与当前上下文相关的重点记忆提取出来，作为系统提示的一部分注入模型，使 AI 随时牢记这些要点。例如，如果站长之前告诉 AI “我的宠物狗叫豆豆”，则后续AI回复中可表现出对这一信息的记忆，不需要用户重复说明。实现方式可以是将重点记忆预先写入系统消息区域，或利用 PromptChatMemoryAdvisor 将重点记忆插入提示文本
    springdoc.cn
    。由于重点记忆内容精炼简短，这样的附加不会明显增加模型负担，却能极大提升交互体验的一致性。

    记忆存储实现：模块采用 JDBC + MySQL 实现持久记忆存储。设计 MemoryRepository 接口，提供保存消息、查询历史等方法，并有两个实现：InMemoryMemoryRepository（用于游客，即数据仅存在于应用内存，生命周期与会话相同）和 JdbcMemoryRepository（用于持久化站长会话记录到数据库）。每条消息记录包括会话ID、发送者（用户或AI）、消息内容、时间戳等信息。对于重点记忆，可在数据库中单独建表，或在消息记录中加标记字段表示“重要”。当站长用户的会话开始时，系统通过会话ID加载最近N条历史记录以及全部重点记忆，恢复对话上下文。

通过以上机制，AI 模块可以在多轮会话中“记住”用户之前说过的话和提供的信息。这种多轮记忆确保了对话的上下文相关性，使AI回答更加贴合用户历史。并且借助 Spring AI 提供的内置 ChatMemoryAdvisor，这些记忆注入过程是自动拦截实现的，无需每次手工拼接
springdoc.cn
。
知识库（世界书）集成

世界书知识库为 AI 提供了额外的背景知识与设定支撑，尤其在用户定义的角色世界观或博客主题领域下。模块通过以下方式集成知识库：

    知识条目与触发：每本世界书由若干条知识条目（Lore）组成，每个条目包含一段描述内容，以及触发其生效的一组关键词（Keys）。当用户消息中出现某些关键词时，对应的条目就会被“激活”，其内容将动态插入到提示中
    docs.sillytavern.app
    。例如，某角色的世界书中有一条描述他的故乡信息，设定关键词为“故乡”、“家乡”等，只要用户的提问提到了这些词，AI 的提示上下文就会附加这段故乡描述，帮助AI据此回答背景相关的问题。值得注意的是，这些世界书条目并非无条件全部提供给 AI，而是按需插入 —— 仅当相关关键词出现时才提供，以控制提示长度并确保内容的针对性
    gtss-ai-docs.barz.foo
    。

    扫描深度：为防止匹配过多无关历史，系统支持配置扫描深度参数，决定在对话历史中检查关键词的范围
    gtss-ai-docs.barz.foo
    。深度为 1 表示只扫描用户的最新一条消息；深度为 2 则扫描最近两条消息，依此类推。通过调整深度，可以避免因上下文过长导致的误触发，也减少性能开销。一般建议默认扫描最近几轮对话即可，这与 SillyTavern 的设计类似（其默认扫描最近2条回复
    reddit.com
    ）。另外还可以设置递归激活选项：即一个条目被插入后，其内容中的关键词又可以触发其他条目。由于可能增加复杂性，我们可选择简化或关闭递归，以免过多内容叠加。

    向量语义检索：除了关键词匹配，系统利用向量数据库实现语义搜索增强。为每个知识条目的内容生成向量嵌入，并存储在例如 Chroma、Milvus 等向量库中。当用户提出问题时，KnowledgeService 可以将用户提问向量化，在向量库中检索最相关的条目内容。这种**RAG（检索增强生成）**模式可以找出那些虽未明确出现关键词但语义相关的知识条目
    docs.spring.io
    。比如用户问“这款产品的原理是什么？”时，知识库中可能没有“原理”这个词作为关键词，但通过语义相似度搜索，仍能找到内容相关的条目（比如包含技术背景介绍的条目）。检索到的相关内容也会作为参考资料加入提示，提升AI回答准确性。

    上下文注入策略：对于被选中的知识条目内容，我们需要决定如何插入到提示上下文中。一种方式是在系统消息中追加一个“知识”部分，将所有激活的条目内容列出；另一种方式是将每个条目作为单独的系统消息或附加在用户提问消息之前。无论哪种方式，都应遵循一定顺序和优先级（例如可以参考 SillyTavern 的“Insertion Order”概念，让重要度高的先插入
    docs.sillytavern.app
    ）。同时设置Token预算，限制知识库内容最多占用的 Token 数
    gtss-ai-docs.barz.foo
    ：如果激活条目过多超出预算，则舍弃低优先级部分，确保不会淹没模型上下文。

    动态管理：知识库管理接口允许用户在前端增删改查世界书及条目。例如站长可以上传一份设定集作为世界书 JSON，然后系统将各条目解析后存储，并批量生成嵌入向量。用户也能在线调整某条目的触发关键词或深度。世界书可全局启用/停用，也可与角色关联启用。实现上，每条 KnowledgeEntry 记录存于数据库，包括其文本内容、关键词列表（可用分隔符或另建关联表）、深度、优先级等字段，以及所属的知识库ID。VectorStore 则存储 id->向量 的映射，提供类似 findRelevant(text, k) 的查询接口。

通过以上机制，AI 模块能够在对话中灵活运用用户定制的知识库信息：像一个动态百科词典，当对话涉及相关话题时才插入对应“词条”解释，既丰富了 AI 的回复内容，又保持了输出的精准和高效
gtss-ai-docs.barz.foo
。
Agent 模式与博客工具调用

Agent 模式下，AI 不仅扮演特定角色与用户对话，还可以代表用户执行操作。在我们的博客场景中，这意味着 AI 能调用博客系统提供的 API 来管理文章。实现此功能需要将博客操作封装成可供 AI 调用的工具，并在对话中引入函数调用机制：

    工具函数定义：首先，我们定义一组博客操作函数（如 createPost(title, content), editPost(id, newContent), deletePost(id) 等），并在AI模块中实现它们调用博客REST API的逻辑。这些函数可以使用 Spring AI 的 @Tool 注解标记描述，或按照 OpenAI Function Calling 格式定义规范。每个函数提供功能说明、参数说明，使得模型明白何种情况下可以使用该函数。

    模型工具调用集成：当 Agent 模式开启时，ChatService 会在调用 LLM 时附加可用的工具函数列表。例如使用 Spring AI ChatClient 的 .tools() 方法注册工具
    或者 .functions() 提供函数签名。模型在生成回复的过程中，如果判断需要调用某个工具（函数）获取信息或执行动作，会在其“思考”步骤中请求调用函数
    。Spring AI 框架的Advisor链会拦截模型的函数调用请求，由应用层的 ToolService 执行实际逻辑，然后将执行结果（例如“文章已删除”或查询到的数据）返回给模型
    。模型再将该结果编入最终给用户的回答中。整个过程对于最终用户而言是无缝的：他们只看到AI完成了请求的操作并回复结果。

    功能范围：借助上述机制，AI 可以执行查询（如“帮我列出未发布的博文”）、创建（“新建一篇标题为X的草稿”）、修改（“把上一篇博文内容改成...”）、删除等操作。工具调用既可用于响应用户明确的操作指令，也可在任务模式下自动触发（例如用户点击“发布文章”按钮后，前端调用AI模块的发布任务接口，AI 内部使用工具函数将文章状态置为发布，并生成一段祝贺语）。

    安全与控制：为了避免模型滥用工具或产生误操作，系统会结合用户权限和会话模式进行约束。只有在Agent模式且用户具备相应权限时（如站长权限），AI提供的工具列表才包括敏感操作（删除文章等）；游客或普通用户可能只允许调用安全的查询类工具。另外可以利用 Spring AI 的 SafeGuardAdvisor 等进行内容审查，确保模型不会在非授权情况下调用工具
    。在实现上，可以在调用 ChatClient 前，根据当前会话和用户角色决定传入哪些工具函数。前端UI也可提供显式的“AI助手模式”开关，以切换 AI 是否可以执行操作。



综上，在 Agent 模式下，博客陪伴AI相当于具备“代理人”能力，可以在聊天中直接帮用户完成博客管理事务。这不仅提升了用户体验的互动性（用户可以自然语言对AI说“帮我发个博文”），也展现了系统集成的强大灵活性：通过函数调用机制，把传统的REST操作融合进对话，让 AI 成为真正的智能助理。
多模型切换与部署

